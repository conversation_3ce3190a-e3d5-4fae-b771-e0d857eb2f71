# 动态时间控制功能说明

## 概述

根据您的要求，我已经修改了 `experiment_flow.py` 和 `experiment_display.py` 文件，为展示问题和答案的功能添加了动态时间控制。

## 修改内容

### 1. `experiment_display.py` 修改

#### `show_question` 方法
- **新增参数**: `duration: float = None`
- **功能**: 
  - 当 `duration=None` 时：等待用户按回车键再切换到下一个界面
  - 当 `duration=数值` 时：等待指定时间（秒）后自动切换到下一个界面
- **提示文本**: 根据 `duration` 参数动态显示不同的提示信息

#### `show_answer` 方法
- **新增参数**: `duration: float = None`（添加到第一个参数位置）
- **功能**: 
  - 当 `duration=None` 时：等待用户按回车键再切换到下一个界面
  - 当 `duration=数值` 时：等待指定时间（秒）后自动切换到下一个界面
- **提示文本**: 根据 `duration` 参数动态显示不同的提示信息

### 2. `experiment_flow.py` 修改

#### 调用方式更新
- `show_question` 调用：添加了 `duration=` 参数名
- `show_answer` 调用：添加了 `duration=` 参数名

## 使用方式

### 在实验配置中设置时间参数

```python
# 在 experiment_flow.py 的 timing 配置中
self.timing = {
    'question_display': 5.0,    # 问题显示5秒后自动切换
    'answer_display': 10.0,     # 答案显示10秒后自动切换
    # 或者设置为 None 来等待用户按回车键
    'question_display': None,   # 等待用户按回车键
    'answer_display': None,     # 等待用户按回车键
}
```

### 方法调用示例

```python
# 自动时间控制（5秒后自动切换）
display.show_question("这是一个问题", duration=5.0)

# 手动控制（等待回车键）
display.show_question("这是一个问题", duration=None)

# 自动时间控制（10秒后自动切换）
display.show_answer("这是答案", duration=10.0)

# 手动控制（等待回车键）
display.show_answer("这是答案", duration=None)
```

## 功能特点

### 1. 灵活的时间控制
- **自动模式**: 指定时间后自动切换，适合标准化实验流程
- **手动模式**: 等待用户确认，适合个性化节奏控制

### 2. 用户友好的提示
- **自动模式**: 显示倒计时提示，如"将在 5.0 秒后自动继续"
- **手动模式**: 显示操作提示，如"按回车键继续"

### 3. 提前退出支持
- 在自动模式下，用户仍可以按回车键提前继续
- 按 Esc 键可以跳过当前显示

### 4. 兼容性保持
- 保持了原有的方法签名兼容性
- 支持虚拟模式和 PsychoPy 模式

## 测试验证

已创建测试脚本验证功能：
- `test_simple_timing.py`: 基本功能测试
- `test_dummy_mode_timing.py`: 虚拟模式详细测试

测试结果显示：
- ✅ `duration=None` 时正确等待用户输入
- ✅ `duration=数值` 时正确等待指定时间
- ✅ 提示文本正确显示
- ✅ 与实验流程集成正常

## 配置建议

### 标准化实验
```python
self.timing = {
    'question_display': 5.0,    # 给被试5秒阅读问题
    'answer_display': 10.0,     # 给被试10秒阅读答案
}
```

### 个性化实验
```python
self.timing = {
    'question_display': None,   # 让被试自己控制阅读节奏
    'answer_display': None,     # 让被试自己控制阅读节奏
}
```

### 混合模式
```python
self.timing = {
    'question_display': None,   # 问题让被试自己控制
    'answer_display': 8.0,      # 答案固定8秒（确保数据一致性）
}
```

## 注意事项

1. **时间精度**: 在虚拟模式下，长时间会被限制为最多2秒以加快测试
2. **按键响应**: 在自动模式下，用户仍可以通过按键提前继续
3. **错误处理**: 如果显示失败，方法会返回 `False`
4. **资源管理**: 使用完毕后记得调用 `display.close()` 清理资源

这个修改完全满足了您的需求：当参数为 `None` 时等待用户按回车键，当参数不为 `None` 时等待指定时间后自动切换。
